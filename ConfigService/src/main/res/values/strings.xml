<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="buy_coin_long_text_des1">Please contact customer service if you encounter a problem with payment.\nThe personal sale of Gold is strictly prohibited. Charges made through unofficial channels will result in bans.\nPayments can only be made by adults.</string>

    <string name="game_room_price_unit">Diamond</string>
    <string name="marry_refresh_tips">The wedding schedule refreshes at 12:00pm daily. Remember to come</string>
    <string name="broadcast_send_tip">Tip: if the post contains abuse or ads, it will be deleted. When you schedule a broadcast, the system will use the attempt that expires the soonest by default.</string>

    <string name="cocos_rank_help_default_content">1. Every cycle of Weekly Rankings lasts from 00:00 Monday to 23:59 Sunday\n2. Games played in the room you create are not counted</string>


    <string name="prop_item_name_marry_ring">Wedding Ring</string>
    <string name="prop_item_name_single_ring">Single\'s Ring</string>
    <string name="prop_item_name_draw_board">Drawing Boards</string>
    <string name="prop_item_name_home_anim">Homepage Effects</string>
    <string name="prop_item_name_head_decoration">Frames</string>
    <string name="prop_item_name_type_prop">Functions</string>
    <string name="prop_item_name_red_packet">Red Packets</string>
    <string name="prop_item_name_packet_skin">Red Packet Cover</string>
    <string name="prop_item_name_voice_bubble">Room Bubbles</string>
    <string name="prop_item_name_chat_bubble">Chat Bubbles</string>
    <string name="prop_item_name_enter_anim">Entrance Effects</string>
    <string name="prop_item_name_discover_decor">Moments Card</string>
    <string name="prop_item_name_family_avatar_decor">Family Frame</string>
    <string name="prop_item_name_family_chat_bubble">Chat Bubbles</string>
    <string name="prop_item_name_family_light">Family Sign</string>
    <string name="prop_item_name_family_coin">Currency</string>
    <string name="prop_item_name_gift_card">Gift Card</string>
    <string name="prop_item_name_voice_theme">Room Theme</string>
    <string name="prop_item_name_user_tag">Title</string>
    <string name="prop_item_name_mic_anim">Mic Animations</string>
    <string name="prop_item_name_car_anim">Car</string>
    <string name="prop_item_voice_room_marking">Voice Room Imprint</string>
    <string name="prop_item_name_jackaroo_piece">Marbles</string>
    <string name="prop_item_name_jackaroo_board">Boards</string>
    <string name="prop_item_name_jackaroo_play_card_animation">Play Effect</string>
    <string name="prop_item_name_jackaroo_kill_animation">Kill Effect</string>
    <string name="prop_item_name_jackaroo_exchange_animation">Swap Effect</string>


    <string name="prop_item_name_family_supplies">Supplies</string>
    <string name="prop_item_dragon_naming">Battle Titling</string>
    <string name="pull_config_again">Pull configuration again</string>
</resources>