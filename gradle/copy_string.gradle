// 执行 ./gradlew :wepie:CopyStrings
import java.io.StringWriter
import java.io.PrintWriter
task CopyStrings {
    doLast {
        def dirList = []
        rootProject.allprojects.forEach { p ->
            def sourceSets = p.android.sourceSets
            if (sourceSets != null) {
                sourceSets.main.res.srcDirs.forEach { dir ->
                    def f = new File(dir.toString(), "values-zh")
                    if (f.exists()) {
                        dirList.add(f)
                    }
                }
            }
        }

        def stringSet = new HashSet<String>()
        def destXml = new Node(null, "resources")

        dirList.each { dir ->
            for (File f in dir.listFiles()) {
                def xmlContent = parseXml(f)
                xmlContent.each { node ->
                    if (stringSet.add(node.attribute("name"))) {
                        destXml.appendNode(node.name(), node.attributes(), node.value())
                    } else {
                        println "Node:${node.attribute("name")} ${node.text()}"
                    }
                }
            }
        }
        printXml(destXml, project.file("total_strings.xml"))
    }
}

task MergeStrings {
    doFirst {
        // doMergeString("values")
        doMergeString("values-en")
        doMergeString("values-ar")
        doMergeString("values-tr")
        doMergeString("values-hi")
        doMergeString("values-zh")
    }
}

task GetAllResDir {
    doFirst {
        def allFiles = []
        rootProject.allprojects.forEach { p ->
            try {
                p.android.sourceSets.main.res.srcDirs.forEach { dir ->
                    def f = new File(dir.toString())
                    if (f.exists()) {
                        allFiles.add(f.getAbsolutePath())
                    }
                }
            } catch (NullPointerException e) {
                //ignore
            } catch (Exception e) {
                e.printStackTrace()
                println("error===" + p + " " + e)
            }
        }
        def f = rootProject.file("build/res_dir.tmp")
        f.createNewFile()
        f.withWriter { writer ->
            allFiles.each { path ->
                writer.write(path)
                writer.write("\n")
            }
        }
    }
}

def doMergeString(String destLang) {
    def translationFile = project(":lib:language").file("build/generated/res/resValues/debug/${destLang}/strings.xml")
    if (!translationFile.exists()) {
        throw IllegalStateException("can not find " + translationFile)
    }
    def translationMap = [:]
    parseXml(translationFile).each { node ->
        translationMap[node.attribute("name")] = node.text()
    }
    def checkMap = [:]
    checkMap.putAll(translationMap)

    rootProject.allprojects.forEach { p ->
        def sourceSets = p.android.sourceSets
        if (sourceSets != null) {
            sourceSets.main.res.srcDirs.forEach { dir ->
                def destValuesDir = new File(dir.toString(), destLang)
                for (File f : destValuesDir.listFiles()) {
                    // 只处理 strings.xml 相关文件
                    if (f.name != "strings.xml" && !f.name.contains("strings")) {
                        continue
                    }

                    // 检查是否有缺失的翻译需要添加，并解析英文文件的完整结构
                    def englishFile = new File(new File(dir.toString(), "values-en"), f.name)
                    def hasMissingTranslations = false
                    def englishKeys = new LinkedHashSet<String>()
                    def englishStructure = [] // 保存英文文件的完整结构，包括注释

                    if (englishFile.exists()) {
                        try {
                            def inResources = false
                            englishFile.readLines().forEach { line ->
                                def trimmedLine = line.trim()

                                // 跳过 XML 头部和 resources 标签
                                if (trimmedLine.startsWith("<?xml") ||
                                    trimmedLine.startsWith("<resources") ||
                                    trimmedLine.equals("</resources>") ||
                                    trimmedLine.isEmpty()) {
                                    return
                                }

                                if (trimmedLine.startsWith("<string name=\"") ||
                                    trimmedLine.startsWith("<string-array name=\"") ||
                                    trimmedLine.startsWith("<plurals name=\"")) {
                                    def key = null
                                    if (trimmedLine.startsWith("<string name=\"")) {
                                        def start = trimmedLine.indexOf("name=\"") + 6
                                        def end = trimmedLine.indexOf("\"", start)
                                        key = trimmedLine.substring(start, end)
                                    } else if (trimmedLine.startsWith("<string-array name=\"")) {
                                        def start = trimmedLine.indexOf("name=\"") + 6
                                        def end = trimmedLine.indexOf("\"", start)
                                        key = trimmedLine.substring(start, end)
                                    } else if (trimmedLine.startsWith("<plurals name=\"")) {
                                        def start = trimmedLine.indexOf("name=\"") + 6
                                        def end = trimmedLine.indexOf("\"", start)
                                        key = trimmedLine.substring(start, end)
                                    }

                                    if (key) {
                                        englishKeys.add(key)
                                        englishStructure.add([type: "key", key: key, line: line])

                                        if (translationMap.containsKey(key)) {
                                            // 检查当前文件是否已包含此key
                                            def currentContent = f.text
                                            if (!currentContent.contains("name=\"${key}\"")) {
                                                hasMissingTranslations = true
                                            }
                                        }
                                    }
                                } else if (trimmedLine.startsWith("</string-array>") ||
                                          trimmedLine.startsWith("</plurals>") ||
                                          trimmedLine.startsWith("<item>") ||
                                          trimmedLine.startsWith("</item>")) {
                                    englishStructure.add([type: "content", line: line])
                                } else if (trimmedLine.startsWith("<!--")) {
                                    // 保留注释
                                    englishStructure.add([type: "comment", line: line])
                                } else if (!trimmedLine.isEmpty()) {
                                    // 其他内容（如空行等）
                                    englishStructure.add([type: "other", line: line])
                                }
                            }
                        } catch (Exception e) {
                            println("Error parsing English file: ${englishFile.absolutePath}, ${e.message}")
                        }
                    }

                    // 检查是否需要添加注释
                    def needsComments = false
                    if (englishStructure.any { it.type == "comment" }) {
                        def currentContent = f.text
                        if (!currentContent.contains("<!--")) {
                            needsComments = true
                        }
                    }

                    // 扫描是否存在游离的 <item>（不在 <plurals>/<string-array> 块内）或异常重复，若存在则强制规范化
                    def needsPluralNormalization = false
                    try {
                        def openComplexCount = 0
                        f.readLines().each { rawLine ->
                            def t = rawLine.trim()
                            if (t.startsWith("<string-array name=\"") || t.startsWith("<plurals name=\"")) {
                                openComplexCount++
                            } else if (t.startsWith("</string-array>") || t.startsWith("</plurals>")) {
                                if (openComplexCount > 0) openComplexCount--
                            } else if (t.startsWith("<item ")) {
                                if (openComplexCount == 0) {
                                    needsPluralNormalization = true
                                }
                            }
                        }
                    } catch (Exception ignore) {}

                    if (hasMissingTranslations || needsComments || needsPluralNormalization) {
                        // 如果有缺失的翻译，按照英文文件的顺序重新组织整个文件
                        def currentTranslations = new HashMap<String, String>()
                        def currentContent = new HashMap<String, List<String>>()
                        def xmlHeader = []
                        def xmlFooter = []
                        def change = false

                        // 解析当前文件，提取所有翻译和内容
                        def lines = f.readLines()
                        def i = 0
                        def inResources = false

                        // 提取 XML 头部
                        while (i < lines.size()) {
                            def line = lines[i]
                            def trimmed = line.trim()
                            if (trimmed.startsWith("<resources")) {
                                xmlHeader.add(line)
                                inResources = true
                                i++
                                break
                            } else if (trimmed.startsWith("<?xml") || trimmed.startsWith("<!--") || trimmed.isEmpty()) {
                                xmlHeader.add(line)
                            }
                            i++
                        }

                        // 解析 resources 内容
                        while (i < lines.size() && inResources) {
                            def line = lines[i]
                            def trimmed = line.trim()

                            if (trimmed.equals("</resources>")) {
                                xmlFooter.add(line)
                                inResources = false
                                i++
                                break
                            } else if (trimmed.startsWith("<string name=\"")) {
                                def key = trimmed.substring(14, trimmed.indexOf("\"", 14))
                                def replace = translationMap.get(key)
                                if (replace != null) {
                                    currentTranslations.put(key, replace)
                                    checkMap.remove(key)
                                    change = true
                                } else {
                                    // 提取现有翻译
                                    def valueStart = trimmed.indexOf(">") + 1
                                    def valueEnd = trimmed.lastIndexOf("<")
                                    if (valueStart > 0 && valueEnd > valueStart) {
                                        def value = trimmed.substring(valueStart, valueEnd)
                                        currentTranslations.put(key, value)
                                    }
                                }
                                currentContent.put(key, [line])
                            } else if (trimmed.startsWith("<string-array name=\"") || trimmed.startsWith("<plurals name=\"")) {
                                def key = trimmed.substring(trimmed.indexOf("name=\"") + 6, trimmed.indexOf("\"", trimmed.indexOf("name=\"") + 6))
                                def content = []
                                content.add(lines[i])
                                i++
                                // 读取完整的块
                                while (i < lines.size()) {
                                    content.add(lines[i])
                                    def blockLine = lines[i].trim()
                                    if (blockLine.startsWith("</string-array>") || blockLine.startsWith("</plurals>")) {
                                        break
                                    }
                                    i++
                                }
                                currentContent.put(key, content)
                            }
                            i++
                        }

                        // 添加剩余的 footer 内容
                        while (i < lines.size()) {
                            xmlFooter.add(lines[i])
                            i++
                        }

                        // 添加缺失的翻译
                        englishKeys.each { key ->
                            if (!currentTranslations.containsKey(key) && translationMap.containsKey(key)) {
                                currentTranslations.put(key, translationMap.get(key))
                                checkMap.remove(key)
                                change = true
                            }
                        }

                        // 重新组织文件内容（按照英文文件的顺序）
                        if (change) {
                            def finalLines = []
                            finalLines.addAll(xmlHeader)

                            // 按照英文文件的完整结构重新组织内容（包括注释）
                            englishStructure.each { item ->
                                if (item.type == "key") {
                                    def key = item.key
                                    if (currentTranslations.containsKey(key)) {
                                        // 这是一个 string 标签，尽量保留原有属性
                                        def value = normalizeAndEscapeValue(currentTranslations.get(key))
                                        def templateLine = currentContent.containsKey(key) ? currentContent.get(key).get(0) : item.line
                                        finalLines.add(buildStringLineFromTemplate(templateLine, key, value))
                                    } else if (currentContent.containsKey(key)) {
                                        // 这是一个 string-array 或 plurals 标签
                                        currentContent.get(key).each { line ->
                                            finalLines.add(line)
                                        }
                                    }
                                } else if (item.type == "comment") {
                                    // 保留注释
                                    finalLines.add(item.line)
                                } else if (item.type == "other") {
                                    // 保留与块无关的其他内容
                                    finalLines.add(item.line)
                                } else if (item.type == "content") {
                                    // 若当前 key 是复杂块（plurals/string-array），content 已由 currentContent 全量输出；
                                    // 若不是复杂块，保持跳过，避免游离的 <item> 被输出
                                }
                            }

                            // 添加不在英文文件中但存在于当前文件中的内容（保持在最后）
                            currentContent.each { key, content ->
                                if (!englishKeys.contains(key)) {
                                    content.each { line ->
                                        finalLines.add(line)
                                    }
                                }
                            }

                            currentTranslations.each { key, value ->
                                if (!englishKeys.contains(key)) {
                                    finalLines.add("    <string name=\"${key}\">${value}</string>")
                                }
                            }

                            finalLines.addAll(xmlFooter)
                            f.write(finalLines.join("\n"))
                        }
                    } else {
                        // 如果没有缺失的翻译，使用原有的简单替换逻辑
                        def change = false
                        def stringLines = new ArrayList<String>()
                        f.readLines().forEach {
                            def s = it.trim()
                            if (s.startsWith("<string name=\"")) {
                                def key = s.substring(14, s.indexOf("\"", 14))
                                def replace = translationMap.get(key)
                                if (replace != null) {
                                    checkMap.remove(key)
                                    change = true
                                    stringLines.add(buildStringLineFromTemplate(it, key, normalizeAndEscapeValue(replace)))
                                } else {
                                    stringLines.add(it)
                                }
                            } else {
                                stringLines.add(it)
                            }
                        }
                        if (change) {
                            f.write(stringLines.join("\n"))
                        }
                    }
                }
            }
        }
    }

    // 将未找到的 key 落到 wepie 模块的兜底文件中，避免丢失
    if (!checkMap.isEmpty()) {
        try {
            def fallbackDir = project(":wepie").file("res/${destLang}")
            if (!fallbackDir.exists()) {
                fallbackDir.mkdirs()
            }
            def fallbackFile = new File(fallbackDir, "strings_merge_extra.xml")
            def existingKeys = new HashSet<String>()
            if (fallbackFile.exists()) {
                try {
                    parseXml(fallbackFile).each { node ->
                        def n = node.attribute("name")
                        if (n) existingKeys.add(n.toString())
                    }
                } catch (Exception ignore) {
                }
            }

            def header = """<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<resources>\n"""
            def footer = "\n</resources>\n"
            def lines = new ArrayList<String>()
            lines.add(header.trim())

            // 如果文件已存在，先保留原有内容（除去重复 key）
            if (fallbackFile.exists()) {
                def inResources = false
                fallbackFile.readLines().each { line ->
                    def t = line.trim()
                    if (t.startsWith("<resources")) {
                        inResources = true
                        return
                    }
                    if (t.equals("</resources>")) {
                        inResources = false
                        return
                    }
                    if (inResources) {
                        if (t.startsWith("<string name=\"")) {
                            def k = t.substring(14, t.indexOf("\"", 14))
                            if (!checkMap.containsKey(k)) {
                                lines.add(line)
                            }
                        } else {
                            lines.add(line)
                        }
                    }
                }
            }

            // 追加剩余缺失 key
            checkMap.keySet().toList().sort().each { k ->
                def v = normalizeAndEscapeValue(checkMap.get(k))
                lines.add("    <string name=\"${k}\">${v}</string>")
            }

            lines.add(footer.trim())
            fallbackFile.write(lines.join("\n"))
        } catch (Exception e) {
            println("write fallback error: ${e.message}")
        }

        checkMap.each { key, value ->
            println("not found " + key + " " + value)
        }
    }
}

def parseXml(File file) {
    def xmlParser = new XmlParser()
    def xmlContent = xmlParser.parse(file)
    return xmlContent
}

def printXml(Node node, File outputFile) {
    outputFile.withWriter { writer ->
        writer.write(printNode(node))
    }
}

def printNode(Node node) {
    def writer = new StringWriter()
    def printer = new XmlNodePrinter(new PrintWriter(writer), "    ", "\"")
    printer.setPreserveWhitespace(true)
    printer.print(node)
    return writer.toString()
}

// 根据模板行构造 string 标签，保留除 name 外的其他属性
def buildStringLineFromTemplate(String templateLine, String key, String value) {
    try {
        def line = templateLine ?: ""
        def ltIndex = line.indexOf("<")
        def indent = ltIndex > 0 ? line.substring(0, ltIndex) : "    "
        def openStart = line.indexOf("<string")
        if (openStart == -1) {
            return indent + "<string name=\"${key}\">" + value + "</string>"
        }
        def openEnd = line.indexOf(">", openStart)
        if (openEnd == -1) {
            return indent + "<string name=\"${key}\">" + value + "</string>"
        }
        def attrs = line.substring(openStart + "<string".length(), openEnd).trim()
        // 去掉现有的 name 属性
        attrs = attrs.replaceAll(/\s*name\s*=\s*"[^"]*"/, "").trim()
        def finalAttrs = attrs.isEmpty() ? "name=\"${key}\"" : "name=\"${key}\" " + attrs
        return indent + "<string " + finalAttrs + ">" + value + "</string>"
    } catch (Exception e) {
        return "    <string name=\"${key}\">" + value + "</string>"
    }
}

// 基础 XML 转义，仅用于文本内容
def escapeXmlValue(String value) {
    if (value == null) return ""
    def v = value
    v = v.replace("&", "&amp;")
    v = v.replace("<", "&lt;")
    v = v.replace(">", "&gt;")
    // 双引号在文本中通常不需要转义，但有时会用于属性，这里只处理单引号与双引号为实体以保险
    v = v.replace('"', "&quot;")
    v = v.replace("'", "&apos;")
    return v
}

// 规整输入文本中的反斜杠转义，然后做 XML 转义
def normalizeAndEscapeValue(String value) {
    if (value == null) return ""
    def v = value
    // 将形如 \' 或 \" 先还原为 ' 和 " 再统一按 XML 转义
    v = v.replace("\\'", "'")
    v = v.replace('\\"', '"')
    return escapeXmlValue(v)
}