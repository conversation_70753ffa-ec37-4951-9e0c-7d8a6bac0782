<?xml version='1.0' encoding='utf-8'?>
<resources xmlns:ns0="http://schemas.android.com/tools" ns0:ignore="MissingTranslation">
    <string name="album_name_all">सभी</string>

    <string name="button_preview">पूर्व दर्शन</string>
    <string name="photo_grid_capture">एक तस्वीर लें</string>
    <string name="empty_text">कोई चित्र या वीडियो नहीं</string>
    <string name="button_ok">OK</string>

    <string name="error_over_count">अधिकतम %1$d फ़ाइलें ही चुनी जा सकती हैं</string>
    <string name="error_file_type">असमर्थित फ़ाइल प्रकार</string>
    <string name="error_type_conflict">एक ही समय में चित्र और वीडियो का चयन नहीं किया जा सकता</string>
    <string name="error_no_video_activity">ऐसा कोई ऐप नहीं है जो वीडियो पूर्वावलोकन का समर्थन करता हो</string>
    <string name="error_over_original_size">"फोटो %1$d M से बड़ा है। अपलोड नहीं किया जा सकता है और मूल फोटो अनचेक कर दी जाएगी"</string>
    <string name="error_over_original_count">"%1$d फोटो %2$d M से बड़ा है। \nअपलोड नहीं किया जा सकता है और मूल फोटो अनचेक कर दी जाएगी"</string>
    <string name="button_original">मूल</string>
    <string name="button_sure_default">पुष्टि करें</string>
    <string name="button_sure">पुष्टि करें(%1$d)</string>
</resources>