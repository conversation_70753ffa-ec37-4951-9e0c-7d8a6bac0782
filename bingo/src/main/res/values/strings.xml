<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="bingo_caps">BINGO</string>
    <string name="bingo_start">Start</string>
    <string name="bingo_play">Play</string>
    <string name="bingo_start_game_price">Price</string>
    <string name="bingo_start_game_mode">Mode</string>
    <string name="bingo_player_list_title">Participating player (%s)</string>
    <string name="bingo_card">Card</string>

    <string name="bingo_small_card_switch">Switch</string>


    <string name="bingo_card_finish_tips1">Waiting for Bingo to start…</string>
    <string name="bingo_card_finish_tips2">Waiting for other players to buy cards…</string>
    <string name="bingo_per_card_price">%s / card</string>


    <string name="bingo_tips_level1">1st Bingo ！</string>
    <string name="bingo_tips_level2">2nd Bingo ！</string>
    <string name="bingo_tips_level3">3rd Bingo ！</string>

    <string name="bingo_player_list_cardnum">X%s</string>
    <string name="bingo_help_title">How to play?</string>

    <string name="bingo_open_conflict_is_show">The Bingo is open, please close the Bingo and try again</string>
    <string name="meme_open_conflict_is_show">The Meme is open, please close the Meme and try again</string>
    <string name="draw_open_conflict_is_show">The Draw is open, please close the Draw and try again</string>
    <string name="win_open_conflict_is_show">The Big Winner is open, please close the Big Winner and try again</string>

    <string name="bingo_close_title">Close Bingo?</string>
    <string name="bingo_close_show">After closing, the Diamond purchased by the player will be automatically returned</string>

    <string name="bingo_return_card_title">Return Card?</string>
    <string name="bingo_return_card_tip">After returning card, the Diamond you paid will be returned</string>

    <string name="bingo_start_game_remind">Other players have been waiting for a long time, please start the Bingo quickly</string>



    <string name="bingo_buy_change">Change</string>
    <string name="bingo_buy_card_tips1">Choose up 2 cards</string>
    <string name="bingo_buy_card_return">Return Card</string>
    <string name="bingo_buy_card_empty">Let’s play Bingo together!</string>


    <string name="bingo_no_award">No Prize</string>
    <string name="bingo_award">Prize</string>

    <string name="bingo_round_ouver">Round\nOver</string>


    <string name="bingo_close">Close</string>
    <string name="bingo_play_again">Play Again</string>
    <string name="bingo_none_bingo">Nobody Bingo</string>
    <string name="bingo_result_1st">1st BINGO</string>
    <string name="bingo_result_2nd">2nd BINGO</string>
    <string name="bingo_result_3rd">3rd BINGO</string>

    <string name="bingo_bar_watch">Watching…</string>

    <string name="bingo_result_prize_num">+%s</string>

    <string name="bingo_watch_round_prize">Round Prize</string>

    <plurals name="bingo_close_time">
        <item quantity="one">Please wait %s second</item>
        <item quantity="other">Please wait %s seconds</item>
    </plurals>

    <string name="bingo_can_not_change_watch">The round is over and cannot be switched</string>

    <string name="bingo_tips_get">get</string>
</resources>