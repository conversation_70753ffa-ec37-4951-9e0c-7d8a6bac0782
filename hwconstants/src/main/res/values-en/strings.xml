<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="server_data_err">Server Data Error</string>
    <string name="werewolf_nine">Werewolf 9 players</string>
    <string name="were_wolf_12">Werewolf 12 players</string>
    <string name="game_type_spy_ab_text">Who\'s Spy Text</string>
    <string name="game_type_spy_ab_voice">Who\'s Spy Voice</string>
    <string name="game_type_spy_blank_text">Wordless Spy Text</string>
    <string name="game_type_music_hum">Custom Show</string>
    <string name="game_type_spy">Who\'s The Spy</string>
    <string name="game_type_voice_match">Voice Chat</string>
    <string name="game_type_draw_guess">Draw &amp; Guess</string>
    <string name="game_type_sing">Draw &amp; Guess</string>
    <string name="game_type_plane_chess">Crazy Airplane Chess</string>

    <string name="game_type_marry">Wedding</string>
    <string name="game_type_room_old">Gang up</string>
    <string name="game_type_ice_ball">Super Iceball</string>
    <string name="game_type_eight_quaver">Eighth Note</string>
    <string name="game_type_landload">Fight the Landlord</string>
    <string name="game_type_voice_room">Voice Room</string>
    <string name="game_type_wedding">Wedding Hall</string>
    <string name="game_type_love_home">Lover Room</string>
    <string name="game_type_cp_home">Acquaint Room</string>
    <string name="game_type_auction">Auction</string>
    <string name="game_type_family_room">Family Voice Room</string>
    <string name="game_type_co_game">Master looking for a mate</string>
    <string name="game_type_say_guess">Act &amp; Guess</string>
    <string name="game_type_xroom">WeParty</string>
    <string name="game_type_baloot">Baloot</string>

    <string name="game_type_short_name_blank">Blank</string>
    <string name="game_type_short_name_sing">Sing &amp; Guess</string>
    <string name="game_type_short_name_draw">Draw &amp; Guess</string>
    <string name="game_type_short_name_marry">Wedding</string>
    <string name="game_type_short_name_double">Spies</string>
    <string name="game_type_short_name_werewolf">Werewolf</string>
    <string name="game_type_short_name_landload">Landlord</string>
    <string name="game_type_short_name_spy_text">Spies - Text</string>
    <string name="game_type_short_name_spy_voice">Spies - Voice</string>
    <string name="game_type_short_name_spy_blank_text">Blank - Text</string>
    <string name="game_type_short_name_draw_guess">Draw &amp; Guess</string>
    <string name="game_type_short_name_music_hum">Mic Grab</string>
    <string name="game_type_short_name_baloot">Baloot</string>

    <string name="game_type_name_suffix_blank">Wordless Spy</string>
    <string name="game_type_name_suffix_draw">Draw &amp; Guess</string>
    <string name="game_type_name_suffix_spy_normal">Spy</string>
    <string name="game_type_name_suffix_spy_text">Spy - Text</string>
    <string name="game_type_name_suffix_spy_voice">Spy - Voice</string>
    <string name="game_type_name_suffix_spy_blank_text">Wordless Spy - Text</string>
    <string name="game_type_name_suffix_spy_blank_voice">Wordless Spy - Voice</string>
    <string name="game_type_name_suffix_music_hum">Mic Grab</string>


    <string name="game_type_unknown">Unknown</string>
    <string name="game_type_spy_desc_mode">Spy - Description</string>
    <string name="game_type_spy_qa_mode">Spy - Question</string>

    <string name="gta_voice">Guess Answers Voice</string>
    <string name="gta_text">Guess Answers Text</string>
</resources>