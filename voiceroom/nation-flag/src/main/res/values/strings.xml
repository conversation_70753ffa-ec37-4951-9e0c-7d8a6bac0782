<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 升旗玩法 -->
    <string name="voice_room_nation_flag_tips_1"><Data><![CDATA[With <font color="#FFE07E">%1$s</font>\'s outstanding contribution,]]></Data></string>
    <string name="voice_room_nation_flag_tips_2"><Data><![CDATA[rose to the top of the galaxy, reaching a height of <font color="#FFE07E">%1$sm</font>]]></Data></string>
    <string name="voice_room_nation_flag_award">Rewards</string>
    <string name="voice_room_nation_flag_step_1">Launch a challenge</string>
    <string name="voice_room_nation_flag_step_2">To raise the national flag</string>
    <string name="voice_room_nation_flag_start_title">The flag-raising challenge begins!</string>
    <string name="voice_room_nation_flag_start_desc">Test begins when it up to 10000m in 10mins!</string>
    <string name="voice_room_nation_flag_fail_title">No flag rises to the top</string>
    <string name="voice_room_nation_flag_fail_desc">This will end in 3s. Sending flags now will not raise the flag</string>
    <string name="voice_room_nation_flag_level_title">%1$s Flag reached %2$s and a new luxury prize won！</string>
    <string name="voice_room_nation_flag_level_desc">Tips: Draw prizes after flags sent</string>
    <string name="voice_room_nation_flag_which_in_top">%1$s finally passed the test \n and rose to the top!</string>
    <string name="voice_room_nation_flag_finish_tips">Tips: Sending flags at this stage will not raise the flag\'s height</string>
    <string name="voice_room_nation_flag_send_gift">To send flags</string>
    <string name="voice_room_nation_flag_detail">Details</string>
    <string name="voice_room_nation_flag_task_desc">Task: Let the national flag reaches 10000m</string>
    <string name="voice_room_nation_flag_height_rank">Flag Height Overview</string>
    <string name="voice_room_nation_flag_count_down">Countdown</string>
    <string name="voice_room_nation_flag_which_flag">Which flag will be the first to rise to the top?</string>
    <string name="voice_room_nation_flag_send_tips">For every flag sent, the flag will rise 10m</string>
    <string name="voice_room_nation_flag_no_send_tips">Sending the flag at this stage will not raise the flag\'s height</string>
    <string name="voice_room_nation_flag_final_height">The final height %1$sm</string>
    <string name="voice_room_nation_flag_top_desc"><Data><![CDATA[Work it out! <font color="#FFE07E">%1$s</font> rose to the top]]></Data></string>
    <string name="voice_room_nation_help_detail">Details</string>
    <string name="voice_room_nation_flag_rank">Rank</string>
    <string name="voice_room_nation_flag_nick_name">Nickname</string>
    <string name="voice_room_nation_flag_height_title">Raise the flag</string>
    <string name="voice_room_nation_flag_rule">Rules</string>
    <string name="voice_room_nation_flag_send_to">Send to</string>
    <string name="voice_room_nation_flag_price">%1$s Diamond &#160;Send</string>
    <string name="voice_room_nation_flag_award_title">Congrats,%1$s passed and reached the top！</string>
    <string name="voice_room_nation_flag_award_tips">You get the following prizes</string>
    <!-- 升旗玩法 -->
</resources>
